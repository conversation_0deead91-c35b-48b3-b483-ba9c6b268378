import { CommitPaymentRequest, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/operator.entities";
import { <PERSON><PERSON>ttp<PERSON><PERSON><PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorPromoRequest,
    OperatorPromoResponse
} from "@entities/operator.entities";
import { sumMajorUnits } from "@utils/operator.utils";
import { Injectable } from "@nestjs/common";

@Injectable()
export class PromoHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {
    
    public async build(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["totalWin", "transactionId", "roundPID"]);
        this.validateRequiredFields(req, ["promoType", "promoRef"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        
        const promoAmount = this.sanitizeAmount(sumMajorUnits(req.request.totalWin));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("promo");

        const promoRequest: OperatorPromoRequest = this.buildOperatorRequest(customer, token, {
            gameId,
            amount: promoAmount,
            currency,
            betId,
            trxId,
            promo: this.buildPromoInfo(req.promoType, req.promoRef, req.freeSpinData)
        });

        return this.buildHttpRequest({
            endpoint: "promo",
            method: "post",
            payload: promoRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorPromoResponse>(response);
        
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
}
