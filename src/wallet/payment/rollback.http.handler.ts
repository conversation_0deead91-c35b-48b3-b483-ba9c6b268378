import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest, RefundRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/operator.entities";
import { Base<PERSON>ttpHand<PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorRollbackRequest,
    OperatorRollbackResponse
} from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";

@Injectable()
export class RollbackHttpHandler extends BaseHttpHandler
    implements HttpHandler<RefundRequest<IntegrationPaymentRequest>, Balance> {
    
    public async build(req): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId"]);
        this.validateRequiredFields(req.request, ["transactionId"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        
        // For rollback, we need the original transaction ID
        const originalTrxId = req.request.transactionId.publicId;

        const rollbackRequest: OperatorRollbackRequest = this.buildOperatorRequest(customer, token, {
            gameId,
            trxId: originalTrxId
        });

        return this.buildHttpRequest({
            endpoint: "rollback",
            method: "post",
            payload: rollbackRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorRollbackResponse>(response);
        
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
}
